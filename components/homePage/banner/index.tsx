"use client"

import React, { useEffect, useState } from "react"
import { Icon } from "@/components/common/Icon"
import { useLanguageStore } from "@/stores/languageStore"
import { useSystemSetting } from "@/stores"
import { extractYouTubeVideoId, isYouTubeUrl, getYouTubeEmbedUrl, getYouTubeThumbnail } from "@/lib/youtube"

export default function Banner() {
  const [data, setData] = useState<any>(null)
  const [isYouTubeVideo, setIsYouTubeVideo] = useState(false)
  const [youTubeEmbedUrl, setYouTubeEmbedUrl] = useState<string>("")
  const [thumbnailUrl, setThumbnailUrl] = useState<string>("")

  const { isRTL } = useLanguageStore()
  const headline = useSystemSetting("HERO_BANNER_HEADLINE")
  const description = useSystemSetting("HERO_BANNER_DESCRIPTION")
  const ctaText = useSystemSetting("HERO_BANNER_CTA_TEXT")
  const ctaUrl = useSystemSetting("HERO_BANNER_CTA_URL")
  const videoUrl = useSystemSetting("HERO_BANNER_VIDEO_URL")

  useEffect(() => {
    const processedData = {
      headline,
      description,
      ctaText,
      ctaUrl,
      videoUrl
    }

    setData(processedData)

    // Check if the video URL is a YouTube URL
    if (videoUrl && isYouTubeUrl(videoUrl)) {
      const videoId = extractYouTubeVideoId(videoUrl)
      if (videoId) {
        setIsYouTubeVideo(true)
        setYouTubeEmbedUrl(getYouTubeEmbedUrl(videoId, {
          autoplay: true,
          mute: true,
          loop: true,
          controls: false,
          modestbranding: true,
          rel: false,
          showinfo: false
        }))
        setThumbnailUrl(getYouTubeThumbnail(videoId, 'maxres'))
      }
    } else {
      setIsYouTubeVideo(false)
      setYouTubeEmbedUrl("")
      setThumbnailUrl("")
    }
  }, [headline, description, ctaText, ctaUrl, videoUrl])
  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Render YouTube iframe or regular video based on URL type */}
      {isYouTubeVideo && youTubeEmbedUrl ? (
        <iframe
          className="absolute inset-0 w-full h-full object-cover border-0"
          src={youTubeEmbedUrl}
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          style={{
            pointerEvents: 'none', // Prevent interaction with YouTube controls
            transform: 'scale(1.02)', // Slightly scale to hide YouTube branding
          }}
        />
      ) : (
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          poster={thumbnailUrl || "/images/jewelry-banner-poster.jpg"}
        >
          {/* Always render <source> with fallback empty string to avoid hydration mismatch */}
          <source src={data?.videoUrl} type="video/mp4" />
        </video>
      )}

      {/* Fallback gradient while loading */}
      <div
        className={`absolute inset-0 bg-gradient-to-br from-secondary-600 to-secondary-700 ${
          (isYouTubeVideo && youTubeEmbedUrl) || data?.videoUrl ? 'opacity-0' : 'opacity-100'
        } transition-opacity duration-1000`}
      />

      {/* Animated pattern fallback */}
      <div className={`absolute inset-0 opacity-20 ${
        (isYouTubeVideo && youTubeEmbedUrl) || data?.videoUrl ? 'opacity-0' : 'opacity-20'
      } transition-opacity duration-1000`}>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(255,215,0,0.1),transparent_50%)]" />
      </div>

      {/* Dark overlay for text readability */}
      <div className="absolute inset-0 bg-black/28" />

      {/* Content Container */}
      <div className="relative z-10 flex items-end justify-center h-full  lg:px-8">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-8 items-center min-h-[17.5vh]">
            <>
              <h1 className="justify-start text-primary-50 text-5xl font-normal leading-[56px]">
                {data?.headline}
              </h1>
              <div>
                <p className="text-lg sm:text-xl text-primary-50 leading-relaxed mb-8">
                  {data?.description}
                </p>
                <a href={data?.ctaUrl} className="flex items-center gap-1">
                  <p className="text-lg sm:text-xl text-primary-300">
                    {data?.ctaText}
                  </p>
                  <Icon
                    name={isRTL ? "arrow-left" : "arrow-right"}
                    size={20}
                    className="ml-2 text-primary-300"
                  />
                </a>
              </div>
            </>
            {/* )} */}
          </div>
        </div>
      </div>
    </div>
  )
}
