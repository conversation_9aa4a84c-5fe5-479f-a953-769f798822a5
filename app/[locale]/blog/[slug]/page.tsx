import BlogDetailPage from "@/components/blogDetails"

interface BlogPageProps {
  params: Promise<{
    locale: string
    slug: string
  }>
}

// export async function generateMetadata({
//   params
// }: {
//   params: { locale: string }
// }) {
//   const { locale } = params

//   return {
//     title: "Blog Details",
//     description: "Details of the blog post"
//   }
// }


const BlogPage: React.FC<BlogPageProps> = async ({ params }) => {
  const { slug } = await params

  return <BlogDetailPage blogSlug={slug} />
}

export default BlogPage
