'use client';

import MarketInsights from '@/components/marketInsights';
import { notFound } from 'next/navigation';
import { useParams } from 'next/navigation';

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default function LocalizedMarketInsightsPage() {
  const params = useParams();
  const locale = params.locale as string;

  // Validate locale
  if (!supportedLocales.includes(locale)) {
    notFound();
  }

  return <MarketInsights />;
}
